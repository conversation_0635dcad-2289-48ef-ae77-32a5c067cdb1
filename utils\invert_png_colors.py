"""
Script pour inverser les couleurs des images PNG
Auteur: Gabriel <PERSON>
Date: 2025-09-08

Ce script prend un dossier d'entrée contenant des images PNG et crée des versions
avec les couleurs inversées dans un dossier de sortie.
"""

import os
import sys
import argparse
from pathlib import Path
import numpy as np
from PIL import Image
import cv2


def invert_png_colors(input_folder, progress_callback=None):
    """
    Inverse les couleurs de toutes les images PNG d'un dossier
    Les images inversées sont sauvegardées dans un sous-dossier "inverse"

    Args:
        input_folder (str): Chemin vers le dossier d'entrée
        progress_callback (function): Fonction de callback pour le progrès (optionnel)

    Returns:
        tuple: (nombre_traités, nombre_erreurs, liste_erreurs, dossier_sortie)
    """
    input_path = Path(input_folder)
    output_path = input_path / "inverse"
    
    # Vérifier que le dossier d'entrée existe
    if not input_path.exists():
        raise FileNotFoundError(f"Le dossier d'entrée n'existe pas: {input_folder}")
    
    # Créer le dossier de sortie s'il n'existe pas
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Trouver tous les fichiers PNG (éviter les doublons)
    png_files = []
    for pattern in ["*.png", "*.PNG"]:
        for file in input_path.glob(pattern):
            if file not in png_files:
                png_files.append(file)
    
    if not png_files:
        print(f"Aucun fichier PNG trouvé dans {input_folder}")
        return 0, 0, []
    
    print(f"Trouvé {len(png_files)} fichiers PNG à traiter")
    
    processed_count = 0
    error_count = 0
    errors = []
    
    for i, png_file in enumerate(png_files):
        try:
            # Callback de progrès
            if progress_callback:
                progress_callback(i + 1, len(png_files), png_file.name)
            
            print(f"Traitement de {png_file.name} ({i+1}/{len(png_files)})")
            
            # Lire l'image
            image = cv2.imread(str(png_file), cv2.IMREAD_UNCHANGED)
            
            if image is None:
                raise ValueError(f"Impossible de lire l'image: {png_file}")
            
            # Inverser les couleurs
            if len(image.shape) == 3:  # Image couleur
                if image.shape[2] == 4:  # RGBA
                    # Inverser RGB, garder le canal alpha
                    inverted = image.copy()
                    inverted[:, :, :3] = 255 - image[:, :, :3]
                else:  # RGB
                    inverted = 255 - image
            else:  # Image en niveaux de gris
                inverted = 255 - image
            
            # Sauvegarder l'image inversée
            output_file = output_path / png_file.name
            success = cv2.imwrite(str(output_file), inverted)
            
            if not success:
                raise ValueError(f"Impossible de sauvegarder l'image: {output_file}")
            
            processed_count += 1
            
        except Exception as e:
            error_msg = f"Erreur avec {png_file.name}: {str(e)}"
            print(f"❌ {error_msg}")
            errors.append(error_msg)
            error_count += 1
    
    return processed_count, error_count, errors, str(output_path)


def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(
        description="Inverse les couleurs des images PNG d'un dossier. "
                   "Les images inversées sont sauvegardées dans un sous-dossier 'inverse'."
    )
    parser.add_argument(
        "input_folder",
        help="Dossier contenant les images PNG d'entrée"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Mode verbeux"
    )
    
    args = parser.parse_args()
    
    try:
        print("🔄 Inversion des couleurs PNG")
        print(f"📂 Dossier d'entrée: {args.input_folder}")
        print("-" * 50)

        processed, errors_count, error_list, output_folder = invert_png_colors(
            args.input_folder
        )

        print(f"📁 Dossier de sortie créé: {output_folder}")
        
        print("-" * 50)
        print(f"✅ Traitement terminé!")
        print(f"📊 Images traitées: {processed}")
        
        if errors_count > 0:
            print(f"❌ Erreurs: {errors_count}")
            if args.verbose:
                print("\nDétail des erreurs:")
                for error in error_list:
                    print(f"  • {error}")
        else:
            print("🎉 Aucune erreur!")
            
    except Exception as e:
        print(f"💥 Erreur fatale: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
