import numpy as np
from pathlib import Path

in_dir = Path(r"C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\SB70_MGO_153020\SB70-MGO-153020-45M-50M-6-07-2024_0178\inverse\masks_visual\npz")
for p in in_dir.glob("*.npz"):
    z = np.load(p)
    if hasattr(z, "files") and "volume" in z.files:
        arr = z["volume"]
        # écrase l'existant avec 'arr_0'
        np.savez_compressed(p, arr)
        print("Converti ->", p)
