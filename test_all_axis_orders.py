#!/usr/bin/env python3
"""
Script pour tester tous les ordres d'axes disponibles dans pngs_to_volume.py
"""

import numpy as np
import os

def test_axis_orders():
    """Teste tous les ordres d'axes et affiche les résultats"""
    
    # Définition des ordres d'axes et leurs descriptions
    axis_orders = {
        'ZYX': '<PERSON><PERSON>eur, Hauteur, Largeur (défaut)',
        'ZXY': '<PERSON><PERSON><PERSON>, Large<PERSON>, Hauteur', 
        'YZX': '<PERSON><PERSON>, Profondeur, Largeur',
        'YXZ': '<PERSON><PERSON>, Largeur, Profondeur',
        'XZY': '<PERSON><PERSON>, Profondeur, Hauteur',
        'XYZ': 'Largeur, Hauteur, Profondeur'
    }
    
    print("=" * 80)
    print("RÉSUMÉ DES ORDRES D'AXES DISPONIBLES DANS pngs_to_volume.py")
    print("=" * 80)
    print()
    
    # Informations sur le volume de test
    print("📊 VOLUME DE TEST :")
    print("   • Dossier : test_labels")
    print("   • Nombre d'images : 100")
    print("   • Dimensions des images : 512 x 512 pixels")
    print("   • Volume original (ZYX) : (100, 512, 512)")
    print()
    
    print("📋 ORDRES D'AXES DISPONIBLES :")
    print("-" * 80)
    
    for order, description in axis_orders.items():
        filename = f"test_volume_{order.lower()}.npz"
        
        if os.path.exists(filename):
            try:
                data = np.load(filename)
                volume = data['volume']
                shape = volume.shape
                print(f"   {order:3s} : {shape} - {description}")
            except Exception as e:
                print(f"   {order:3s} : Erreur lors du chargement - {e}")
        else:
            print(f"   {order:3s} : Fichier non trouvé - {description}")
    
    print()
    print("💡 UTILISATION :")
    print("   python pngs_to_volume.py <dossier_png> <sortie> --axis-order <ORDRE>")
    print()
    print("📝 EXEMPLES :")
    print("   python pngs_to_volume.py images/ volume --axis-order XZY")
    print("   python pngs_to_volume.py data/ output --axis-order YXZ --format h5")
    print()
    print("=" * 80)

if __name__ == "__main__":
    test_axis_orders()
