#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Crée des masques vides (classe background=0, noir) pour toutes les images
présentes dans le dossier A mais absentes du dossier B.

- Dossier A : images source
- Dossier B : masques déjà existants (ex: masks_visual)
- Dossier OUT : masks_empty (sera créé)

On compare par "stem" (nom de fichier sans extension), avec une petite
normalisation pour tolérer des suffixes fréquents côté masques
(_mask, _masks, _visual, -mask, -masks, -visual).
"""

from pathlib import Path
from PIL import Image

# --- Chemins hardcodés ---
PATH_A = Path(r"C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\test_imagesTr")
PATH_B = Path(r"C:\Users\<USER>\Documents\datasets\Suzlon\Prod_data_dataset\test_labelTr")
PATH_OUT = PATH_B.parent / "masks_empty"  # inverse\masks_empty

# Extensions d'images supportées (source et masques)
IMG_EXTS = {".png", ".jpg", ".jpeg", ".bmp", ".tif", ".tiff"}
# On accepte aussi des masques non-images pour la détection de présence (au cas où)
MASK_EXTRA_EXTS = {".npz", ".npy"}

# Suffixes éventuels côté masques (ex: nom_image -> nom_image_visual.png)
SUFFIXES_TO_STRIP = ("_mask", "_masks", "_visual", "_mask_visual", "-mask", "-masks", "-visual")


def norm_stem(stem: str) -> str:
    """Normalise un stem en retirant quelques suffixes fréquents."""
    s = stem
    for suf in SUFFIXES_TO_STRIP:
        if s.endswith(suf):
            s = s[: -len(suf)]
    return s


def list_files(root: Path, exts: set[str]) -> list[Path]:
    """Liste récursive des fichiers avec extensions données."""
    files = []
    for p in root.rglob("*"):
        if p.is_file() and p.suffix.lower() in exts:
            files.append(p)
    return files


def main():
    if not PATH_A.exists():
        raise FileNotFoundError(f"Dossier A introuvable: {PATH_A}")
    if not PATH_B.exists():
        print(f"⚠️ Dossier B introuvable: {PATH_B} (on considérera qu'aucun masque n'existe)")

    PATH_OUT.mkdir(parents=True, exist_ok=True)

    # Images source (A)
    images_A = list_files(PATH_A, IMG_EXTS)
    if not images_A:
        print(f"❌ Aucune image trouvée dans A: {PATH_A}")
        return

    # Masques existants (B) — on prend images + extras (npz/npy) pour couvrir large
    masks_B = list_files(PATH_B, IMG_EXTS | MASK_EXTRA_EXTS) if PATH_B.exists() else []

    # Ensemble des stems de B (originaux + normalisés)
    stems_B = set()
    for m in masks_B:
        stem = m.stem
        stems_B.add(stem)
        stems_B.add(norm_stem(stem))

    # Trouver les images sans masque
    missing = []
    for img in images_A:
        s = img.stem
        if (s not in stems_B) and (norm_stem(s) not in stems_B):
            missing.append(img)

    print(f"📷 Images dans A : {len(images_A)}")
    print(f"🖼️ Masques trouvés dans B : {len(masks_B)}")
    print(f"❔ À créer (masques vides) : {len(missing)}")
    if not missing:
        print("✅ Rien à faire. Tous les masques semblent déjà présents.")
        return

    created = 0
    for i, img_path in enumerate(missing, 1):
        try:
            with Image.open(img_path) as im:
                w, h = im.size  # (width, height)

            # Crée un masque 'L' (8-bit) noir de même taille
            mask = Image.new("L", (w, h), 0)

            out_name = img_path.stem + ".png"  # on sort en .png
            out_path = PATH_OUT / out_name

            # Éviter d'écraser si déjà présent (au cas où relance)
            if out_path.exists():
                print(f"SKIP (existe déjà): {out_path}")
                continue

            mask.save(out_path)
            created += 1

            if i % 50 == 0 or i == len(missing):
                print(f"📈 Progression: {i}/{len(missing)} (créés: {created})")

        except Exception as e:
            print(f"⚠️ Erreur pour {img_path}: {e}")

    print(f"✅ Terminé. Masques vides créés: {created} → {PATH_OUT}")


if __name__ == "__main__":
    main()
